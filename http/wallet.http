# ========================== HTTP Client 自动登录脚本 ==========================

# 环境变量配置
@baseUrlForLogin = http://paydev:8080
@baseUrl = http://localhost:8080
#@baseUrl = http://paydev:8080
#@baseUrl = http://paytest:8080
@tenantId = K99999999
@clientId = e5cd7e4891bf95d1d19206ce24a7b32e
@grantType = password
@username = admin
@password = admin123


### 1. login Token (明文请求 - 需要关闭API加密)
POST {{baseUrlForLogin}}/auth/login
Content-Type: application/json

{
  "tenantId": "{{tenantId}}",
  "clientId": "{{clientId}}",
  "grantType": "{{grantType}}",
  "username": "{{username}}",
  "password": "{{password}}"
}

> {%
    // 自动提取并存储访问令牌
    if (response.status === 200) {
        const responseBody = response.body;
        if (responseBody.code === 200 && responseBody.data && responseBody.data.access_token) {
            client.global.set("access_token", responseBody.data.access_token);
            client.global.set("refresh_token", responseBody.data.refresh_token);
            client.global.set("expire_in", responseBody.data.expire_in);
            console.log("登录成功，Token已保存");
            console.log("Access Token: " + responseBody.data.access_token);
            console.log("Token有效期: " + responseBody.data.expire_in + "秒");
        } else {
            console.log("登录失败: " + responseBody.msg);
        }
    } else {
        console.log("登录请求失败，HTTP状态码: " + response.status);
    }
%}

### 1.1 切换租户
GET {{baseUrlForLogin}}/system/tenant/dynamic/K99999999
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}


### 2. 使用Token刷新访问令牌（当令牌过期时使用）
POST {{baseUrlForLogin}}/auth/refresh
Content-Type: application/json

{
  "clientId": "{{clientId}}",
  "refreshToken": "{{refresh_token}}"
}

> {%
    // 自动更新访问令牌
    if (response.status === 200) {
        const responseBody = response.body;
        if (responseBody.code === 200 && responseBody.data && responseBody.data.access_token) {
            client.global.set("access_token", responseBody.data.access_token);
            client.global.set("expire_in", responseBody.data.expire_in);
            console.log("Token刷新成功");
        }
    }
%}

### 3. 测试Token有效性
GET {{baseUrl}}/system/user/profile
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

<> 2025-06-20T161424.200.json
<> 2025-06-20T152339.200.json
<> 2025-06-19T204055.200.json
<> 2025-06-19T202243.200.json
<> 2025-06-19T201920.200.txt

### 查询每个钱包最新记录
GET {{baseUrl}}/wallet/coinrec/latest/page?chainType=tron&tokenSymbol=usdt&pageNum=1&pageSize=10
Authorization: Bearer {{access_token}}
clientid: {{clientId}}


<> 2025-06-20T162457.200.txt
<> 2025-06-20T162155.200.txt
<> 2025-06-20T161624.200.txt
<> 2025-06-20T161427.200.json
<> 2025-06-20T161416.200.json

### collect-tron
POST {{baseUrl}}/wallet/coinrec/collect
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

{
    "fromAddress": "TRy4wLctZfdRikaf1fsk7vsbTMWFrBcgvZ",
    "chainType": "TRON",
    "tokenSymbol": "USDC",
    "amount": 600
}


### collect-bsc
POST {{baseUrl}}/wallet/coinrec/collect
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

{
    "fromAddress": "******************************************",
    "chainType": "BSC",
    "tokenSymbol": "USDT",
    "amount": 10
}

### collect-solana
POST {{baseUrl}}/wallet/transfer/collect
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

{
    "fromAddress": "4CPiGUw9MxGvt2qPTVnuwYqUBvkeh7atZbeKcNGNMAe9",
    "chainType": "SOLANA",
    "tokenSymbol": "USDT",
    "amount": 100000
}

### rec
POST {{baseUrl}}/wallet/coinrec/scan/TTuhNkJvT8vWBHjHpavwKAEMVemZK6tMKM/TRON
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}


### 查询资金钱包余额
GET {{baseUrl}}/wallet/transfer/getCapitalWallet?chain=TRON
Authorization: Bearer {{access_token}}
clientid: {{clientId}}


### withdraw
POST {{baseUrl}}/wallet/transfer/withdraw
Content-Type: application/json
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

[
    {
        "requestId": 24104,
        "toAddress": "TP98LnUSEggNgh38nfs4mRaMTjkPtGCufE",
        "amount": 10.50,
        "tokenSymbol": "USDT",
        "chainType": "TRON",
        "memo": "TRON USDT提款"
    },
    {
        "requestId": 2411212,
        "toAddress": "******************************************",
        "amount": 1,
        "tokenSymbol": "USDT",
        "chainType": "BSC",
        "memo": "USDT BNB提款"
    }
]

### withdraw-select
GET {{baseUrl}}/aset/payCoin/listForWithdrawals?pageSize=50&pageNum=1&txnCode=c1010&txnStatus=0
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### withdraw-exec
GET {{baseUrl}}/aset/payCoin/listForWithdrawals?pageSize=10&pageNum=1&recStatus=-1&txnCode=c1010&txnStatus=2
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 手动交易补偿evm
POST {{baseUrl}}/wallet/scan/manual/evm/BSC/tx/0x3def7c6aa96111338a9777f3c93fc072df54a6e74784634826df185dfdf16660
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 手动交易补偿tron
POST {{baseUrl}}/wallet/scan/manual/tron/tx/ab330c20577e9e885cbd4d37fc430ad09ce6545a7fd390fe614f6134b2f05d7c
Authorization: Bearer {{access_token}}
clientid: {{clientId}}


### 使用说明：
# 1. 首先执行 "用户登录获取Token" 请求，系统会自动保存access_token
# 2. 后续所有业务请求都会自动使用 {{access_token}} 和 {{clientId}} 变量
# 3. 当token过期时，执行 "刷新访问令牌" 请求更新token
# 4. 所有token相关变量都会自动保存在全局环境中
# 5. 注意：业务请求必须同时包含Authorization和clientid请求头才能通过验证
#
# 环境变量说明：
# - baseUrl: 服务器基础地址
# - tenantId: 租户ID（默认000000）
# - clientId: OAuth2客户端ID
# - username/password: 登录凭据
# - access_token: 自动保存的访问令牌
# - refresh_token: 自动保存的刷新令牌
#
#



# ========================== Solana监控管理API ==========================

### 获取Solana监控状态
GET {{baseUrl}}/api/solana/monitor/status
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 获取Solana订阅信息列表
GET {{baseUrl}}/api/solana/monitor/subscriptions
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 手动触发Solana重连
POST {{baseUrl}}/api/solana/monitor/reconnect
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 获取Solana监控摘要
GET {{baseUrl}}/api/solana/monitor/summary
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

