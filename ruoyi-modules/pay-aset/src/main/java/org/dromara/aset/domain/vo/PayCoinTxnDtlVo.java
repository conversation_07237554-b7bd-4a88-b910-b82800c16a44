package org.dromara.aset.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.aset.api.domain.vo.RemoteCoinTxnDtlVo;
import org.dromara.aset.domain.PayCoinTxnDtl;
import org.dromara.wallet.api.model.RemoteTransferStatusDto;
import org.dromara.wallet.api.model.RemoteWalletDto;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/26
 */
@Data
@AutoMapper(target = RemoteCoinTxnDtlVo.class)
public class PayCoinTxnDtlVo {

    /**
     * 自增长流水id
     */
    private Long txnId;

    /**
     * 币种网络
     */
    private String coinNet;

    /**
     * 客户编号
     */
    private Long userId;

    /**
     * 0支出  1收入
     */
    private String recordType;

    /**
     * d1010充值|c1010提币|t1010转账
     */
    private String txnCode;

    /**
     * 链上交易号
     */
    private String receiptNo;

    /**
     * 交易钱包付款人
     */
    private String fromAddress;

    /**
     * 交易收款人
     */
    private String toAddress;

    /**
     * 交易货币(USDT)
     */
    private String txnCoin;

    /**
     * 交易状态:0-待审核，1-已完成，2-已拒绝
     */
    private String txnStatus;

    /**
     * 交易描述
     */
    private String txnDesc;

    /**
     * 交易金额
     */
    private BigDecimal txnAmount;

    /**
     * 手续费
     */
    private BigDecimal txnFee;

    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date txnTime;

    /**
     * 客户资产余额
     */
    private BigDecimal userBalance;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 客户名称
     */
    private String userName;

    /**
     * 客户UID
     */
    private String nickName;

    /**
     * 交易类型详情
     */
    private String txnCodeDetail;

    /**
     * 交易状态详情
     */
    private String statueDetail;

    /**
     * 交易状态详情展示
     */
    private String statueDetailShow;

    // ========== 转账状态扩展属性 ==========

    /**
     * 转账状态详细信息
     * 包含确认状态、区块信息、重试次数等完整信息
     * 通过批量查询钱包服务获取
     */
    private RemoteTransferStatusDto transferStatus;
}
