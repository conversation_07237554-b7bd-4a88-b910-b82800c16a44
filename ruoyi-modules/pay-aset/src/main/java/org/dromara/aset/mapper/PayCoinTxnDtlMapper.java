package org.dromara.aset.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.aset.domain.PayCoinTxnDtl;
import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * <AUTHOR>
 * @date 2025/6/26
 */
@Mapper
public interface PayCoinTxnDtlMapper extends BaseMapperPlus<PayCoinTxnDtl, PayCoinTxnDtlVo> {

    Page<PayCoinTxnDtlVo> selectWithdrawalPage(@Param("page") Page<PayCoinTxnDtlVo> page, @Param(Constants.WRAPPER) Wrapper<PayCoinTxnDtl> queryWrapper);

    int processRefundFromFrozenFunds(PayCoinTxnDtlBo bo);

    // 7个不同表，需要基于tenant_id选择 - 统一使用与Meta相同的参数签名
    int processRefundFromFrozenFundsForBitago(@Param("txnId") Long txnId, @Param("txnStatus") Integer txnStatus);

    int processRefundFromFrozenFundsForMeta(@Param("txnId") Long txnId, @Param("txnStatus") Integer txnStatus);

    int processRefundFromFrozenFundsForRokutel(@Param("txnId") Long txnId, @Param("txnStatus") Integer txnStatus);

    int processRefundFromFrozenFundsForSwipex(@Param("txnId") Long txnId, @Param("txnStatus") Integer txnStatus);

    int processRefundFromFrozenFundsForVoopay(@Param("txnId") Long txnId, @Param("txnStatus") Integer txnStatus);

    int processRefundFromFrozenFundsForXcode(@Param("txnId") Long txnId, @Param("txnStatus") Integer txnStatus);

    int processRefundFromFrozenFundsForZombie(@Param("txnId") Long txnId, @Param("txnStatus") Integer txnStatus);
}
