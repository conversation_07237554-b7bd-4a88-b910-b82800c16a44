package org.dromara.aset.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.aset.domain.PayCoinTxnDtl;
import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.mapper.PayCoinTxnDtlMapper;
import org.dromara.aset.service.IPayCoinTxnDtlService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.wallet.api.RemoteWalletService;
import org.dromara.wallet.api.model.RemoteBatchTransferStatusRequest;
import org.dromara.wallet.api.model.RemoteBatchTransferStatusResponse;
import org.dromara.wallet.api.model.RemoteTransferStatusDto;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/25
 */
@Service
@RequiredArgsConstructor
@DubboService
@Slf4j
public class PayCoinTxnDtlServiceImpl implements IPayCoinTxnDtlService {

    private final PayCoinTxnDtlMapper baseMapper;

    @DubboReference
    private final RemoteWalletService remoteWalletService;

    @Override
    public boolean processRefundFromFrozenFunds(PayCoinTxnDtlBo bo) {
        return baseMapper.processRefundFromFrozenFunds(bo) > 0;
    }

    /**
     * 查询转账记录
     *
     * @param id 主键
     * @return 转账记录
     */
    @Override
    public PayCoinTxnDtlVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询提款记录（需要查询pay-api-wallet）
     * 集成批量查询转账状态功能
     */
    @Override
    public TableDataInfo<PayCoinTxnDtlVo> queryWithdrawals(PayCoinTxnDtlBo bo, PageQuery pageQuery) {
        try {
            // 1. 构建查询条件 - 只查询提币记录
            LambdaQueryWrapper<PayCoinTxnDtl> lqw = buildWithdrawalQueryWrapper(bo);

            // 2. 分页查询提币记录
            IPage<PayCoinTxnDtlVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
            List<PayCoinTxnDtlVo> records = page.getRecords();

            if (records.isEmpty()) {
                return TableDataInfo.build(page);
            }

            // 3. 批量查询转账状态并填充扩展属性
            enrichWithTransferStatus(records);

            return TableDataInfo.build(page);

        } catch (Exception e) {
            throw new RuntimeException("查询提款记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查询提款记录（需要联表）
     */
    @Override
    public TableDataInfo<PayCoinTxnDtlVo> queryWithdrawalsOld(PayCoinTxnDtlBo bo, PageQuery pageQuery) {

        QueryWrapper<PayCoinTxnDtl> wrapper = Wrappers.query();
        wrapper.eq("c.txn_code", bo.getTxnCode())
            .eq("c.txn_status", bo.getTxnStatus())
            .and(w -> {
                if (bo.getRecStatus() == null || bo.getRecStatus() == -1) {
                    w.isNull("w.id");
                } else {
                    w.eq("w.status", bo.getRecStatus());
                }
            })
            .orderByAsc("c.create_time");

        Page<PayCoinTxnDtlVo> payCoinTxnDtlVoPage = baseMapper.selectWithdrawalPage(pageQuery.build(), wrapper);
        return TableDataInfo.build(payCoinTxnDtlVoPage);

    }

    /**
     * 根据条件分页查询币种交易记录
     *
     * @param bo        查询条件业务对象
     * @param pageQuery 分页查询参数
     * @return 分页查询结果
     */
    @Override
    public IPage<PayCoinTxnDtlVo> queryPageByParams(PayCoinTxnDtlBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoPage(pageQuery.build(), lqw);
    }

    /**
     * 根据条件查询币种交易记录
     *
     * @param bo 查询条件业务对象
     * @return 币种交易记录列表
     */
    @Override
    public List<PayCoinTxnDtlVo> queryListByParams(PayCoinTxnDtlBo bo) {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 保存币种交易记录
     *
     * @param payCoinTxnDtl 币种交易记录
     * @return 保存结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCoinTxnDtl(PayCoinTxnDtl payCoinTxnDtl) {
        return baseMapper.insert(payCoinTxnDtl) > 0;
    }

    /**
     * 构建查询条件Wrapper
     *
     * @param bo 查询条件业务对象
     * @return LambdaQueryWrapper查询条件
     */
    private LambdaQueryWrapper<PayCoinTxnDtl> buildQueryWrapper(PayCoinTxnDtlBo bo) {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = new LambdaQueryWrapper<>();

        // 主键查询
        lqw.eq(bo.getTxnId() != null, PayCoinTxnDtl::getTxnId, bo.getTxnId());

        // 用户相关查询
        lqw.eq(bo.getUserId() != null, PayCoinTxnDtl::getUserId, bo.getUserId());
        lqw.in(bo.getUserIds() != null && !bo.getUserIds().isEmpty(), PayCoinTxnDtl::getUserId, bo.getUserIds());

        // 交易类型查询
        lqw.eq(StringUtils.isNotBlank(bo.getTxnCode()), PayCoinTxnDtl::getTxnCode, bo.getTxnCode());
        lqw.in(bo.getTxnTypeList() != null && !bo.getTxnTypeList().isEmpty(), PayCoinTxnDtl::getTxnCode, bo.getTxnTypeList());

        // 交易状态查询
        lqw.eq(bo.getTxnStatus() != null, PayCoinTxnDtl::getTxnStatus, bo.getTxnStatus());

        // 交易ID列表查询
        lqw.in(bo.getTxnIds() != null && !bo.getTxnIds().isEmpty(), PayCoinTxnDtl::getTxnId, bo.getTxnIds());

        // 时间范围查询
        lqw.ge(StringUtils.isNotBlank(bo.getStartDate()), PayCoinTxnDtl::getTxnTime, bo.getStartDate());
        lqw.le(StringUtils.isNotBlank(bo.getEndDate()), PayCoinTxnDtl::getTxnTime, bo.getEndDate());

        // 排序：按交易时间倒序
        lqw.orderByDesc(PayCoinTxnDtl::getTxnTime);

        return lqw;
    }

    /**
     * 构建提款记录查询条件
     */
    private LambdaQueryWrapper<PayCoinTxnDtl> buildWithdrawalQueryWrapper(PayCoinTxnDtlBo bo) {
        LambdaQueryWrapper<PayCoinTxnDtl> lqw = new LambdaQueryWrapper<>();

        // 基础查询条件
        if (bo.getUserId() != null) {
            lqw.eq(PayCoinTxnDtl::getUserId, bo.getUserId());
        }
        if (bo.getUserIds() != null && !bo.getUserIds().isEmpty()) {
            lqw.in(PayCoinTxnDtl::getUserId, bo.getUserIds());
        }

        // 交易类型：默认查询提币记录
        String txnCode = (bo.getTxnCode() != null && !bo.getTxnCode().isEmpty()) ? bo.getTxnCode() : "c1010";
        lqw.eq(PayCoinTxnDtl::getTxnCode, txnCode);

        // 交易状态
        if (bo.getTxnStatus() != null) {
            lqw.eq(PayCoinTxnDtl::getTxnStatus, bo.getTxnStatus());
        }

        // 时间范围查询
        if (bo.getStartDate() != null && !bo.getStartDate().isEmpty()) {
            lqw.ge(PayCoinTxnDtl::getTxnTime, bo.getStartDate());
        }
        if (bo.getEndDate() != null && !bo.getEndDate().isEmpty()) {
            lqw.le(PayCoinTxnDtl::getTxnTime, bo.getEndDate());
        }

        // 只查询有链上交易哈希的记录（即已发起转账的记录）
        lqw.isNotNull(PayCoinTxnDtl::getReceiptNo);
        lqw.ne(PayCoinTxnDtl::getReceiptNo, "");

        // 排序：按交易时间倒序
        lqw.orderByDesc(PayCoinTxnDtl::getTxnTime);

        return lqw;
    }

    /**
     * 批量查询转账状态并填充到PayCoinTxnDtlVo中
     */
    private void enrichWithTransferStatus(List<PayCoinTxnDtlVo> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        try {
            // 1. 提取所有的交易哈希作为requestId（这里假设receiptNo就是requestId）
            @NotNull List<String> requestIds = records.stream()
                .map(PayCoinTxnDtlVo::getTxnId)
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());

            if (requestIds.isEmpty()) {
                return;
            }

            // 2. 构建批量查询请求
            RemoteBatchTransferStatusRequest request = RemoteBatchTransferStatusRequest.builder()
                .requestIds(requestIds)
                .includeDetails(true)
                .onlyFailures(false)
                .onlyProcessing(false)
                .build();

            // 3. 调用Dubbo接口批量查询
            RemoteBatchTransferStatusResponse response = remoteWalletService.batchQueryTransferStatus(request);

            if (response == null || !Boolean.TRUE.equals(response.getSuccess())) {
                return;
            }

            // 4. 将转账状态信息填充到PayCoinTxnDtlVo中
            Map<String, RemoteTransferStatusDto> statusMap = response.getResults();
            if (statusMap != null && !statusMap.isEmpty()) {
                fillTransferStatusToRecords(records, statusMap);
            }

        } catch (Exception e) {
            // 不抛出异常，避免影响主查询流程
        }
    }

    /**
     * 将转账状态信息填充到记录中
     */
    private void fillTransferStatusToRecords(List<PayCoinTxnDtlVo> records, Map<String, RemoteTransferStatusDto> statusMap) {
        for (PayCoinTxnDtlVo record : records) {
            String txnId = String.valueOf(record.getTxnId());

            RemoteTransferStatusDto status = statusMap.get(txnId);
            if (status == null) {
                // 未找到转账状态，设置默认的空状态
                RemoteTransferStatusDto notFoundStatus = RemoteTransferStatusDto.builder()
                    .requestId(txnId)
                    .status(-1)
                    .statusDescription("未找到转账记录")
                    .errorMessage("未找到指定交易哈希的转账记录")
                    .totalRecords(0)
                    .confirmedRecords(0)
                    .failedRecords(0)
                    .build();
                record.setTransferStatus(notFoundStatus);
            } else {
                // 直接设置完整的转账状态对象
                record.setTransferStatus(status);
            }
        }
    }

}
