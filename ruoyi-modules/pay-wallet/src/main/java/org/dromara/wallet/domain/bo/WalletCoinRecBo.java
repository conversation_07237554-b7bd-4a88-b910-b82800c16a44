package org.dromara.wallet.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.AssertTrue;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.tenant.core.TenantEntity;
import org.dromara.wallet.domain.WalletCoinRec;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * 多链用户钱包代币余额记录业务对象
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WalletCoinRec.class, reverseConvertGenerate = false)
public class WalletCoinRecBo extends TenantEntity {

    /**
     * ID标识
     */
    @NotNull(message = "ID标识不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 钱包地址
     */
    @NotBlank(message = "钱包地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String walletAddress;

    /**
     * 代币合约地址
     */
    @NotBlank(message = "代币合约地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tokenAddress;

    /**
     * 代币符号
     */
    private String tokenSymbol;

    /**
     * 可读余额
     */
    @NotNull(message = "可读余额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal balance;

    /**
     * 代币小数位数
     */
    @NotNull(message = "代币小数位数不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer decimals;

    /**
     * 链类型
     */
    private String chainType;

    // ========== 查询筛选条件字段 ==========

    /**
     * 最小余额筛选条件（大于等于此值）
     */
    private BigInteger minRawBalance;

    /**
     * 最大余额筛选条件（小于等于此值）
     */
    private BigInteger maxRawBalance;

    /**
     * 是否只查询最新记录（每个钱包地址和代币组合只返回最新的一条记录）
     */
    private Boolean onlyLatest;

    /**
     * 验证余额范围的有效性
     *
     * @return true 如果余额范围有效，false 否则
     */
    @AssertTrue(message = "最小余额不能大于最大余额")
    public boolean isValidBalanceRange() {
        if (minRawBalance != null && maxRawBalance != null) {
            return minRawBalance.compareTo(maxRawBalance) <= 0;
        }
        return true;
    }

    /**
     * 验证最小余额的有效性
     *
     * @return true 如果最小余额有效，false 否则
     */
    @AssertTrue(message = "最小余额必须大于等于0")
    public boolean isValidMinRawBalance() {
        if (minRawBalance != null) {
            return minRawBalance.compareTo(BigInteger.ZERO) >= 0;
        }
        return true;
    }

    /**
     * 验证最大余额的有效性
     *
     * @return true 如果最大余额有效，false 否则
     */
    @AssertTrue(message = "最大余额必须大于等于0")
    public boolean isValidMaxRawBalance() {
        if (maxRawBalance != null) {
            return maxRawBalance.compareTo(BigInteger.ZERO) >= 0;
        }
        return true;
    }
}
