package org.dromara.wallet.config.tron;

import lombok.Getter;
import lombok.Setter;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.domain.dto.MetaMainAddress;
import org.dromara.wallet.wallet.exception.WalletException;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Objects;

/**
 * TRON钱包配置
 * 扁平化配置 - 钱包相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "tron.wallet")
public class TronWalletConfig {

    /**
     * 是否启用TRON钱包功能
     * -- GETTER --
     *  是否启用

     */
    @Getter
    private boolean enabled = true;



    /**
     * 主钱包地址列表
     */
    private List<MetaMainAddress> mainAddressList;

    /**
     * 手续费钱包配置
     */
    private FeeWalletConfig feeWallet;

    /**
     * 通过sysId获取对应主钱包
     *
     * @return 主钱包地址，如果不存在则返回null
     */
    public MetaMainAddress getMainAddress() {
        if (mainAddressList == null || mainAddressList.isEmpty()) {
            throw new WalletException("TRON主钱包地址列表为空");
        }

        for (MetaMainAddress item : mainAddressList) {
            if (Objects.equals(TenantHelper.getTenantId(), item.getTenantId())) {
                return item;
            }
        }
        throw new WalletException("当前租户tenantId:" + TenantHelper.getTenantId() + ",对应TRON主钱包地址不存在");
    }

    /**
     * 手续费钱包配置
     */
    @Getter
    @Setter
    public static class FeeWalletConfig {
        /**
         * 是否启用手续费钱包
         */
        private boolean enabled = true;

        /**
         * 手续费钱包私钥
         */
        private String privateKey;

        /**
         * 手续费钱包地址
         */
        private String address;

        // ============ 统一费用控制配置 ============

        /**
         * 最大费用限制（单位：sun，1 TRX = 1,000,000 sun）
         * 防止交易费用过高，类似于EVM链的maxGasPrice概念
         */
        private long maxFeeLimit = 5000000L; // 5 TRX

        /**
         * 最大能量燃烧限制
         * 防止单笔交易消耗过多能量
         */
        private long maxEnergyBurn = 50000L;

        /**
         * 能量不足时是否自动燃烧TRX支付手续费
         */
        private boolean autoBurnEnabled = true;

        /**
         * 带宽不足时是否自动燃烧TRX支付手续费
         */
        private boolean autoBurnBandwidthEnabled = true;

        // ============ 能量代理API配置 ============

        /**
         * 是否启用能量代理API功能
         * 通过第三方API为用户地址提供能量，用于支付TRON交易手续费
         */
        private boolean energyProxyEnabled = false;

        /**
         * 能量代理API地址
         * 例如：http://api.example.com/energy
         */
        private String energyProxyUrl = "";

        /**
         * API密钥参数名
         * 例如：key、token等
         */
        private String energyProxyKey = "key";

        /**
         * API密钥值
         * 实际的API密钥
         */
        private String energyProxyValue = "";

        /**
         * 能量提供小时数
         * 为用户地址提供多少小时的能量
         */
        private int energyProxyHour = 1;

        /**
         * 验证手续费钱包配置
         */
        public void validate() {
            if (enabled) {
                if (privateKey == null || privateKey.trim().isEmpty()) {
                    throw new IllegalArgumentException("Fee wallet private key is required when fee wallet is enabled");
                }
                if (address == null || address.trim().isEmpty()) {
                    throw new IllegalArgumentException("Fee wallet address is required when fee wallet is enabled");
                }

                // 验证费用控制配置
                if (maxFeeLimit <= 0) {
                    throw new IllegalArgumentException("Max fee limit must be positive");
                }
                if (maxEnergyBurn <= 0) {
                    throw new IllegalArgumentException("Max energy burn must be positive");
                }

                // 验证能量代理API配置
                if (energyProxyEnabled) {
                    if (energyProxyUrl == null || energyProxyUrl.trim().isEmpty()) {
                        throw new IllegalArgumentException("Energy proxy URL is required when energy proxy is enabled");
                    }
                    if (energyProxyValue == null || energyProxyValue.trim().isEmpty()) {
                        throw new IllegalArgumentException("Energy proxy value is required when energy proxy is enabled");
                    }
                    if (energyProxyHour <= 0) {
                        throw new IllegalArgumentException("Energy proxy hour must be positive");
                    }
                }
            }
        }

        // ============ 便捷方法 ============

        /**
         * 获取费用限制（TRX单位）
         */
        public double getMaxFeeLimitInTrx() {
            return maxFeeLimit / 1_000_000.0;
        }

        /**
         * 设置费用限制（TRX单位）
         */
        public void setMaxFeeLimitInTrx(double trxAmount) {
            this.maxFeeLimit = (long) (trxAmount * 1_000_000);
        }

        /**
         * 是否启用能量代理API
         * 需要同时满足：开关启用 + URL配置 + 密钥配置
         */
        public boolean isEnergyProxyEnabled() {
            return energyProxyEnabled
                && energyProxyUrl != null && !energyProxyUrl.trim().isEmpty()
                && energyProxyValue != null && !energyProxyValue.trim().isEmpty();
        }
    }

    /**
     * 验证钱包配置
     */
    public void validate() {
        if (enabled) {
//            if (mainAddressList == null || mainAddressList.isEmpty()) {
//                throw new IllegalArgumentException("Main address list cannot be empty when TRON wallet is enabled");
//            }

//            // 验证主钱包地址
//            for (MetaMainAddress address : mainAddressList) {
//                if (address.getSysId() == null || address.getSysId().trim().isEmpty()) {
//                    throw new IllegalArgumentException("Main address sysId cannot be empty");
//                }
//                if (address.getAddress() == null || address.getAddress().trim().isEmpty()) {
//                    throw new IllegalArgumentException("Main address cannot be empty");
//                }
//            }

            // 验证手续费钱包配置
            if (feeWallet != null) {
                feeWallet.validate();
            }
        }
    }

    /**
     * 是否启用手续费钱包
     */
    public boolean isFeeWalletEnabled() {
        return enabled && feeWallet != null && feeWallet.isEnabled();
    }

    /**
     * 获取手续费钱包私钥
     */
    public String getFeeWalletPrivateKey() {
        return isFeeWalletEnabled() ? feeWallet.getPrivateKey() : null;
    }

    /**
     * 获取手续费钱包地址
     */
    public String getFeeWalletAddress() {
        return isFeeWalletEnabled() ? feeWallet.getAddress() : null;
    }

    // ============ 费用控制配置访问方法 ============

    /**
     * 获取最大费用限制
     */
    public long getMaxFeeLimit() {
        return feeWallet != null ? feeWallet.getMaxFeeLimit() : 5000000L; // 默认5 TRX
    }

    /**
     * 获取最大能量燃烧限制
     */
    public long getMaxEnergyBurn() {
        return feeWallet != null ? feeWallet.getMaxEnergyBurn() : 50000L;
    }

    /**
     * 是否启用自动燃烧TRX支付能量
     */
    public boolean isAutoBurnEnabled() {
        return feeWallet != null ? feeWallet.isAutoBurnEnabled() : true;
    }

    /**
     * 是否启用自动燃烧TRX支付带宽
     */
    public boolean isAutoBurnBandwidthEnabled() {
        return feeWallet != null ? feeWallet.isAutoBurnBandwidthEnabled() : true;
    }

    // ============ 能量代理API配置访问方法 ============

    /**
     * 是否启用能量代理API
     */
    public boolean isEnergyProxyEnabled() {
        return isFeeWalletEnabled() && feeWallet.isEnergyProxyEnabled();
    }

    /**
     * 获取能量代理API地址
     */
    public String getEnergyProxyUrl() {
        return isFeeWalletEnabled() ? feeWallet.getEnergyProxyUrl() : "";
    }

    /**
     * 获取能量代理API密钥参数名
     */
    public String getEnergyProxyKey() {
        return isFeeWalletEnabled() ? feeWallet.getEnergyProxyKey() : "key";
    }

    /**
     * 获取能量代理API密钥值
     */
    public String getEnergyProxyValue() {
        return isFeeWalletEnabled() ? feeWallet.getEnergyProxyValue() : "";
    }

    /**
     * 获取能量代理小时数
     */
    public int getEnergyProxyHour() {
        return isFeeWalletEnabled() ? feeWallet.getEnergyProxyHour() : 1;
    }
}
