package org.dromara.wallet.wallet.monitor.event;

import lombok.Getter;
import org.dromara.wallet.config.ChainType;
import org.springframework.context.ApplicationEvent;

/**
 * 区块链钱包余额变动事件
 *
 * <AUTHOR>
 * @date 2025/6/26 15:51
 **/
@Getter
public class BalanceChangeEvent extends ApplicationEvent {

    private final ChainType chainType;

    private final String address;

    private final String walletTenantId;

    public BalanceChangeEvent(Object source, ChainType chainType, String address, String walletTenantId) {
        super(source);
        this.chainType = chainType;
        this.address = address;
        this.walletTenantId = walletTenantId;
    }
}
