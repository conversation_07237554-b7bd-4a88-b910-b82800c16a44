package org.dromara.wallet.wallet.transfer.strategy.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.wallet.config.facade.SolanaConfigFacade;
import org.dromara.wallet.config.solana.SolanaRpcConfig;
import org.dromara.wallet.config.solana.SolanaWalletConfig;
import org.dromara.wallet.wallet.exception.BlockchainTransferException;
import org.dromara.wallet.wallet.helper.SolanaHelper;
import org.dromara.wallet.wallet.transfer.alert.TronTransferAlertService;
import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.dto.*;
import org.dromara.wallet.wallet.transfer.exception.SolanaTransferException;
import org.dromara.wallet.wallet.transfer.monitor.SolanaTransferMetrics;
import org.dromara.wallet.wallet.transfer.strategy.AbstractTransferStrategy;
import org.p2p.solanaj.core.Account;
import org.p2p.solanaj.core.PublicKey;
import org.p2p.solanaj.core.Transaction;
import org.p2p.solanaj.programs.SystemProgram;
import org.p2p.solanaj.programs.TokenProgram;
import org.p2p.solanaj.rpc.RpcClient;
import org.p2p.solanaj.rpc.RpcException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * Solana转账策略实现 - 完整迁移版本
 *
 * <p>封装Solana链的转账逻辑，支持以下特性：</p>
 * <ul>
 *   <li>SOL原生代币转账</li>
 *   <li>SPL合约代币转账</li>
 *   <li>手续费钱包支持</li>
 *   <li>交易确认和验证</li>
 *   <li>双格式私钥支持（Base64和Base58）</li>
 *   <li>策略类内置完整转账实现</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
@Component
public class SolanaTransferStrategy extends AbstractTransferStrategy {

    // ==================== 依赖注入 ====================

    private final SolanaHelper solanaHelper; // 保留用于基础API调用
    private final SolanaConfigFacade solanaFacade;
    private final SolanaRpcConfig solanaRpcConfig;

    /**
     * 监控指标收集器
     */
    @Autowired(required = false)
    private SolanaTransferMetrics transferMetrics;

    /**
     * 告警服务（复用TRON的告警服务）
     */
    @Autowired(required = false)
    private TronTransferAlertService alertService;

    /**
     * 构造函数
     * 注入依赖并设置事件发布器
     */
    public SolanaTransferStrategy(SolanaHelper solanaHelper,
                                  SolanaConfigFacade solanaFacade,
                                  SolanaRpcConfig solanaRpcConfig,
                                  ApplicationEventPublisher eventPublisher) {
        this.solanaHelper = solanaHelper;
        this.solanaFacade = solanaFacade;
        this.solanaRpcConfig = solanaRpcConfig;
        setEventPublisher(eventPublisher);
    }

    @Override
    public String getChainName() {
        return "SOLANA";
    }

    @Override
    public boolean supports(String chainName) {
        return "SOLANA".equalsIgnoreCase(chainName) || "SOL".equalsIgnoreCase(chainName);
    }

    // ==================== 抽象方法实现 ====================

    @Override
    protected void executeTransferInternal(TransferRequest request, BlockchainTransferResult result) {
        long startTime = System.currentTimeMillis();
        boolean isNativeToken = isNativeToken(request.getTokenSymbol());
        String transferType = isNativeToken ? "SOL" : "SPL";
        final String[] txHashHolder = {null}; // 使用数组来解决lambda变量引用问题

        try {
            // 1. 记录转账开始
            recordTransferStart(transferType);

            // 2. 使用统一异常转换器，简化异常处理
            solanaHelper.executeWithSolanaExceptionHandling(() -> {
                // 3. 统一手续费处理（增强错误处理）
                FeeProvisionResult feeResult = handleFeeProvisionWithRetry(request);

                // 4. 执行具体转账逻辑（增强错误处理）
                String hash = executeTransferWithRetry(request, isNativeToken);
                txHashHolder[0] = hash; // 保存txHash

                // 5. 异步转账确认（不阻塞转账流程）
                TransactionConfirmationResult confirmationResult = waitForTransactionConfirmationAsyncWithRecord(
                    hash, transferType + "转账", request, result.getTransferRecordId());

                // 6. 设置转账结果
                result.setSuccess(true);
                result.setTxHash(hash);
                result.setFeeProvided(feeResult.isProvided());
                result.setFeeAmount(feeResult.getAmount());
                result.setFeeTokenSymbol(feeResult.getTokenSymbol());
                result.setWaitedForConfirmation(confirmationResult.isConfirmed());
                result.setConfirmations(confirmationResult.getActualConfirmations());
                result.setConfirmationResult(confirmationResult);
                result.setConfirmationTime(confirmationResult.getEndTime());

                // 7. 记录转账成功
                long transferTime = System.currentTimeMillis() - startTime;
                long confirmationTime = confirmationResult.getConfirmationTimeMs();
                recordTransferSuccess(transferTime, confirmationTime, feeResult.getAmount(), feeResult.isProvided());

                // 8. 如果确认失败且配置要求确认失败导致转账失败，则抛出异常
                if (!confirmationResult.isConfirmed() && getConfirmationConfig(request).isConfirmationFailureCausesTransferFailure()) {
                    throw new BlockchainTransferException("Solana转账确认失败: " + confirmationResult.getErrorMessage(),
                        "SOLANA", "CONFIRMATION_FAILED");
                }

                log.info("Solana转账完成: txHash={}, type={}, time={}ms, feeProvided={}, confirmed={}",
                        hash, transferType, transferTime, feeResult.isProvided(), confirmationResult.isConfirmed());

                return null;
            });

        } catch (Exception e) {
            // 9. 增强异常处理
            long transferTime = System.currentTimeMillis() - startTime;
            handleTransferException(e, txHashHolder[0], transferTime, request);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    @Override
    protected String deriveFromAddress(String privateKey) {
        return getOwnerAddressFromPrivateKey(privateKey);
    }

    @Override
    protected FeeEstimate estimateTransferFee(TransferRequest request, boolean isNativeToken) {
        // {{ AURA-X: Modify - 使用统一的手续费估算方法，简化逻辑. Approval: 寸止(ID:1678886400). }}
        // 统一使用estimateSolanaFee()方法，因为SOL和SPL代币转账费用相同
        SolanaHelper.SolanaFeeEstimate solanaEstimate = solanaHelper.estimateSolanaFee();

        return SolanaFeeEstimate.forSolTransfer(
            solanaEstimate.lamportsNeeded(),
            solanaEstimate.solNeeded()
        );
    }

    @Override
    protected boolean needsFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate) {
        return solanaHelper.shouldUseFeeWallet(userAddress, 0L, feeEstimate.getNativeTokenNeeded());
    }

    @Override
    protected FeeProvisionResult provideFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate) {
        boolean success = solanaHelper.provideSolanaFee(userAddress, feeEstimate.getNativeTokenNeeded());

        if (success) {
            return FeeProvisionResult.feeWalletSuccess(
                feeEstimate.getNativeTokenNeeded(),
                feeEstimate.getNativeTokenSymbol()
            );
        } else {
            return FeeProvisionResult.failure("手续费钱包提供SOL失败");
        }
    }

    @Override
    protected void validateTransferRequest(TransferRequest request) {
        // 调用父类基础验证
        super.validateTransferRequest(request);

        // Solana特定验证
        validateSolanaRequest(request);
    }

    @Override
    protected String getNativeTokenSymbol() {
        return "SOL";
    }

    // ==================== 核心转账实现 ====================

    /**
     * 执行SOL原生代币转账
     */
    private String executeNativeTransfer(TransferRequest request) {
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        String toAddress = request.getToAddress();
        BigDecimal amount = request.getAmountAsBigDecimal();

        long lamports = amount.multiply(BigDecimal.valueOf(1_000_000_000L)).longValue();

        log.info("Solana原生代币转账开始: from={}, to={}, amount={} SOL ({} lamports)",
            fromAddress, toAddress, amount, lamports);

        String txHash = transferNative(request.getPrivateKey(), toAddress, lamports);

        log.info("Solana原生代币转账成功: txHash={}", txHash);
        return txHash;
    }

    /**
     * 执行SPL代币转账
     */
    private String executeTokenTransfer(TransferRequest request) {
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        String toAddress = request.getToAddress();
        BigDecimal amount = request.getAmountAsBigDecimal();
        String tokenSymbol = request.getTokenSymbol();

        String contractAddress = solanaFacade.getContractAddress(tokenSymbol);
        if (contractAddress == null || contractAddress.trim().isEmpty()) {
            throw new RuntimeException("Solana链不支持代币: " + tokenSymbol);
        }

        int decimals = solanaFacade.getContractDecimals(tokenSymbol);
        long rawAmount = amount.multiply(BigDecimal.TEN.pow(decimals)).longValue();

        log.info("Solana代币转账开始: from={}, to={}, token={}, amount={} {} (raw: {})",
            fromAddress, toAddress, tokenSymbol, amount, tokenSymbol, rawAmount);

        String txHash = transferToken(request.getPrivateKey(), toAddress, rawAmount, contractAddress);

        log.info("Solana代币转账成功: txHash={}", txHash);
        return txHash;
    }

    // ==================== 基础转账方法 ====================

    /**
     * 发起SOL原生代币交易（基础方法，仅内部使用）
     * 支持双格式私钥（Base64和Base58）
     */
    private String transferNative(String sourcePrivateKey, String toAddress, long lamports) {
        RpcClient client = solanaRpcConfig.getRpcClient();
        // 解码私钥（支持Base64和Base58格式）
        byte[] decode = decodePrivateKey(sourcePrivateKey);
        Account signer = new Account(decode);
        //目标账户
        PublicKey toPublicKey = new PublicKey(toAddress);

        //转账对象
        Transaction transaction = new Transaction();
        transaction.addInstruction(SystemProgram.transfer(signer.getPublicKey(), toPublicKey, lamports));

        try {
            return client.getApi().sendTransaction(transaction, signer);
        } catch (RpcException e) {
            log.error("Solana原生代币转账失败: to={}, amount={}, error={}",
                toAddress, lamports, e.getMessage());
            throw new BaseException("发起SOL交易失败:" + e.getMessage());
        }
    }

    /**
     * 发起SPL代币交易（基础方法，仅内部使用）
     */
    private String transferToken(String sourcePrivateKey, String destinationAccount,
                                 long amount, String tokenMintAddress) {
        RpcClient client = solanaRpcConfig.getRpcClient();

        try {
            // 解码私钥（支持Base64和Base58格式）
            byte[] decode = decodePrivateKey(sourcePrivateKey);
            Account signer = new Account(decode);

            //目标账户
            PublicKey destinationPublicKey = new PublicKey(destinationAccount);
            //代币mint
            PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);

            //源token账户
            PublicKey sourceTokenPublicKey = client.getApi().getTokenAccountsByOwner(signer.getPublicKey(), tokenMintPublicKey);

            //目标mint
            PublicKey destinationTokenPublicKey = client.getApi().getTokenAccountsByOwner(destinationPublicKey, tokenMintPublicKey);

            //转账对象
            Transaction transaction = new Transaction();
            transaction.addInstruction(TokenProgram.transferChecked(
                sourceTokenPublicKey,
                destinationTokenPublicKey,
                amount,
                (byte) 6,
                signer.getPublicKey(),
                tokenMintPublicKey));
            return client.getApi().sendTransaction(transaction, signer);
        } catch (RpcException e) {
            log.error("Solana代币转账失败: to={}, token={}, amount={}, error={}",
                destinationAccount, tokenMintAddress, amount, e.getMessage());
            throw new BaseException("发起Token交易失败:" + e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 30; // Solana策略优先级较低
    }

    @Override
    public boolean isAvailable() {
        try {
            // 检查SolanaHelper是否可用
            return solanaHelper != null;
        } catch (Exception e) {
            log.warn("Solana转账策略不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证Solana转账请求参数
     */
    private void validateSolanaRequest(TransferRequest request) {
        // 基础验证
        request.validate();

        // Solana特定验证
        String tokenSymbol = request.getTokenSymbol().toUpperCase();

        // 验证代币符号
        if (tokenSymbol.length() < 2 || tokenSymbol.length() > 10) {
            throw new IllegalArgumentException("Solana代币符号长度应在2-10个字符之间");
        }

        // 验证地址格式（Solana地址是Base58编码，长度通常在32-44个字符）
        String toAddress = request.getToAddress();
        if (toAddress.length() < 32 || toAddress.length() > 44) {
            throw new IllegalArgumentException("Solana地址格式不正确，长度应在32-44个字符之间");
        }

        // 验证地址字符集（Base58不包含0、O、I、l）
        if (toAddress.matches(".*[0OIl].*")) {
            throw new IllegalArgumentException("Solana地址格式不正确，不能包含字符0、O、I、l");
        }

        // 验证金额范围
        BigDecimal amount = request.getAmountAsBigDecimal();
        if (amount.compareTo(new BigDecimal("0.000000001")) < 0) {
            throw new IllegalArgumentException("Solana转账金额过小，最小值为0.000000001");
        }

        // 验证私钥格式（支持Base64和Base58两种格式）
        String privateKey = request.getPrivateKey();
        if (!isValidSolanaPrivateKey(privateKey)) {
            throw new IllegalArgumentException("Solana私钥格式不正确，应为Base64或Base58格式");
        }

        log.debug("Solana转账请求验证通过: token={}, amount={}, to={}",
            tokenSymbol, request.getAmount(), toAddress);
    }

    /**
     * 验证Solana私钥格式
     */
    private boolean isValidSolanaPrivateKey(String privateKey) {
        if (privateKey == null || privateKey.trim().isEmpty()) {
            return false;
        }

        // Base64格式检查（通常88个字符，以=结尾）
        if (privateKey.length() == 88 && privateKey.endsWith("=")) {
            try {
                java.util.Base64.getDecoder().decode(privateKey);
                return true;
            } catch (Exception e) {
                // 不是有效的Base64
            }
        }

        // Base58格式检查（通常64个字符）
        if (privateKey.length() >= 32 && privateKey.length() <= 88) {
            // 简单的Base58字符集检查
            return privateKey.matches("[1-9A-HJ-NP-Za-km-z]+");
        }

        return false;
    }

    // ==================== 辅助方法 ====================

    /**
     * 解码私钥 - 支持Base64和Base58格式
     */
    private byte[] decodePrivateKey(String privateKey) {
        return solanaHelper.decodePrivateKey(privateKey);
    }

    /**
     * 从私钥推导发送方地址 - 委托给SolanaHelper
     */
    private String getOwnerAddressFromPrivateKey(String privateKey) {
        return solanaHelper.getOwnerAddressFromPrivateKey(privateKey);
    }

    // ==================== 交易确认机制 ====================

    @Override
    protected TransactionConfirmationConfig getConfirmationConfig(TransferRequest request) {
        // Solana链确认配置：30秒超时，2秒间隔，基于400ms区块时间但考虑网络延迟
        return TransactionConfirmationConfig.solanaDefault();
    }

    // ==================== 内部数据类已移至SolanaHelper ====================

    @Override
    public String getDescription() {
        return "Solana区块链转账策略，支持SOL和SPL代币，支持双格式私钥";
    }

    // ==================== 增强错误处理方法 ====================

    /**
     * 带重试的手续费处理
     */
    private FeeProvisionResult handleFeeProvisionWithRetry(TransferRequest request) {
        int maxRetries = 2;
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return handleFeeProvision(request);
            } catch (Exception e) {
                lastException = e;
                log.warn("Solana手续费处理失败，第{}次尝试: {}", attempt, e.getMessage());

                if (attempt < maxRetries) {
                    // 等待后重试
                    try {
                        Thread.sleep(1000L * attempt); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 所有重试都失败，抛出Solana特定异常
        throw SolanaTransferException.fromGenericException(lastException, null);
    }

    /**
     * 带重试的转账执行
     */
    private String executeTransferWithRetry(TransferRequest request, boolean isNativeToken) {
        int maxRetries = getMaxRetriesForTransfer();
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (isNativeToken) {
                    return executeNativeTransfer(request);
                } else {
                    return executeTokenTransfer(request);
                }
            } catch (Exception e) {
                lastException = e;
                SolanaTransferException solanaException = convertToSolanaException(e, null);

                log.warn("Solana转账执行失败，第{}次尝试: type={}, error={}",
                        attempt, solanaException.getErrorType().getDescription(), e.getMessage());

                // 检查是否可重试
                if (!solanaException.isRetryable() || attempt >= maxRetries) {
                    break;
                }

                // 等待后重试
                try {
                    Thread.sleep(2000L * attempt); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 所有重试都失败，抛出Solana特定异常
        throw convertToSolanaException(lastException, null);
    }

    /**
     * 转账异常处理
     */
    private void handleTransferException(Exception e, String txHash, long transferTime, TransferRequest request) {
        // 转换为Solana特定异常
        SolanaTransferException solanaException = convertToSolanaException(e, txHash);

        // 记录异常指标
        recordTransferFailure(solanaException.getErrorType().name(), transferTime);

        // 发送告警
        sendExceptionAlert(solanaException, txHash, buildExceptionContext(request));

        log.error("Solana转账异常处理: type={}, txHash={}, time={}ms, retryable={}, advice={}",
                solanaException.getErrorType().getDescription(), txHash, transferTime,
                solanaException.isRetryable(), solanaException.getRecoveryAdvice());
    }

    /**
     * 转换为Solana特定异常
     */
    private SolanaTransferException convertToSolanaException(Exception e, String txHash) {
        if (e instanceof SolanaTransferException) {
            return (SolanaTransferException) e;
        }
        return SolanaTransferException.fromGenericException(e, txHash);
    }

    /**
     * 构建异常上下文信息
     */
    private String buildExceptionContext(TransferRequest request) {
        return String.format("Chain=SOLANA, Token=%s, Amount=%s, To=%s",
                request.getTokenSymbol(), request.getAmount(), request.getToAddress());
    }

    /**
     * 获取转账最大重试次数
     */
    private int getMaxRetriesForTransfer() {
        return 2; // 默认重试2次
    }

    // ==================== 监控指标记录方法 ====================

    /**
     * 记录转账开始
     */
    private void recordTransferStart(String transferType) {
        if (transferMetrics != null) {
            transferMetrics.recordTransferStart(transferType);
        }
    }

    /**
     * 记录转账成功
     */
    private void recordTransferSuccess(long transferTime, long confirmationTime,
                                     BigDecimal feeAmount, boolean feeProvided) {
        if (transferMetrics != null) {
            transferMetrics.recordTransferSuccess(transferTime, confirmationTime, feeAmount, feeProvided);
        }
    }

    /**
     * 记录转账失败
     */
    private void recordTransferFailure(String exceptionType, long transferTime) {
        if (transferMetrics != null) {
            transferMetrics.recordTransferFailure(exceptionType, transferTime);
        }
    }

    /**
     * 发送异常告警
     */
    private void sendExceptionAlert(SolanaTransferException exception, String txHash, String context) {
        if (alertService != null && isAlertingEnabled()) {
            // 将Solana异常转换为通用告警格式
            alertService.sendCustomAlert(
                exception.getAlertLevel(),
                "Solana转账异常告警",
                exception.toString(),
                context
            );
        }
    }

    /**
     * 检查是否启用监控
     */
    private boolean isMonitoringEnabled() {
        return transferMetrics != null;
    }

    /**
     * 检查是否启用告警
     */
    private boolean isAlertingEnabled() {
        return alertService != null && isMonitoringEnabled();
    }
}
