package org.dromara.wallet.wallet.transfer.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * 转账完成事件
 *
 * <p>用于在转账确认完成后通知业务层，实现异步业务处理机制</p>
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>转账确认完成后立即发布</li>
 *   <li>支持成功和失败两种状态</li>
 *   <li>提供完整的转账上下文信息</li>
 *   <li>业务层可异步监听处理</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>提现完成后更新业务系统状态</li>
 *   <li>充值确认后触发业务逻辑</li>
 *   <li>归集完成后的后续处理</li>
 *   <li>转账失败后的业务补偿</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Getter
public class TransferCompletionEvent extends ApplicationEvent {

    /**
     * 转账请求ID
     * 用于关联业务请求和转账结果
     */
    private final String requestId;

    /**
     * 交易哈希
     * 区块链上的交易标识符
     */
    private final String transactionHash;

    /**
     * 区块链名称
     * 如：BSC、TRON、Solana等
     */
    private final String chainName;

    /**
     * 转账类型
     * 如：withdraw(提现)、deposit(充值)、collect(归集)、native(原生代币)、token(代币)
     */
    private final String transferType;

    /**
     * 转账是否成功
     * true-成功，false-失败
     */
    private final boolean success;

    /**
     * 转账记录ID
     * 关联到meta_transfer_rec表的主键ID
     */
    private final Long transferRecordId;

    /**
     * 错误信息
     * 转账失败时的详细错误描述
     */
    private final String errorMessage;

    /**
     * 完成时间
     * 转账确认完成的时间
     */
    private final Date completionTime;

    /**
     * 确认数量
     * 区块链确认的区块数量
     */
    private final int confirmations;


    private final String coinType;
    private final String amount;
    private final String tenantId;
    private final String tokenValue;

    /**
     * 发送地址（from地址）
     * 用于余额刷新等后续处理
     */
    private final String fromAddress;

    /**
     * 接收地址（to地址）
     * 用于余额刷新等后续处理
     */
    private final String toAddress;

    /**
     * 创建转账完成事件
     *
     * @param source           事件源对象
     * @param requestId        转账请求ID
     * @param transactionHash  交易哈希
     * @param chainName        区块链名称
     * @param transferType     转账类型
     * @param success          是否成功
     * @param transferRecordId 转账记录ID
     * @param errorMessage     错误信息（失败时）
     * @param confirmations    确认数量
     * @param tenantId
     * @param tokenValue
     * @param fromAddress      发送地址
     * @param toAddress        接收地址
     */
    public TransferCompletionEvent(Object source,
                                   String requestId,
                                   String transactionHash,
                                   String chainName,
                                   String transferType,
                                   boolean success,
                                   Long transferRecordId,
                                   String errorMessage,
                                   Integer confirmations,
                                   String coinType,
                                   String amount, String tenantId, String tokenValue,
                                   String fromAddress, String toAddress) {
        super(source);
        this.requestId = requestId;
        this.transactionHash = transactionHash;
        this.chainName = chainName;
        this.transferType = transferType;
        this.success = success;
        this.transferRecordId = transferRecordId;
        this.errorMessage = errorMessage;
        this.coinType = coinType;
        this.amount = amount;
        // 安全处理confirmations，避免NPE
        this.confirmations = confirmations != null ? confirmations : 0;
        this.tenantId = tenantId;
        this.tokenValue = tokenValue;
        this.fromAddress = fromAddress;
        this.toAddress = toAddress;
        this.completionTime = new Date();
    }

    /**
     * 创建成功的转账完成事件
     *
     * @param source           事件源对象
     * @param requestId        转账请求ID
     * @param transactionHash  交易哈希
     * @param chainName        区块链名称
     * @param transferType     转账类型
     * @param transferRecordId 转账记录ID
     * @param confirmations    确认数量
     * @param fromAddress      发送地址
     * @param toAddress        接收地址
     * @return 转账完成事件
     */
    public static TransferCompletionEvent success(Object source,
                                                  String requestId,
                                                  String transactionHash,
                                                  String chainName,
                                                  String transferType,
                                                  Long transferRecordId,
                                                  Integer confirmations,
                                                  String coinType,
                                                  String amount,
                                                  String tenantId,
                                                  String tokenValue,
                                                  String fromAddress,
                                                  String toAddress) {
        return new TransferCompletionEvent(source, requestId, transactionHash, chainName,
            transferType, true, transferRecordId, null, confirmations, coinType, amount, tenantId, tokenValue, fromAddress, toAddress);
    }

    /**
     * 创建失败的转账完成事件
     *
     * @param source           事件源对象
     * @param requestId        转账请求ID
     * @param transactionHash  交易哈希
     * @param chainName        区块链名称
     * @param transferType     转账类型
     * @param transferRecordId 转账记录ID
     * @param errorMessage     错误信息
     * @param fromAddress      发送地址
     * @param toAddress        接收地址
     * @return 转账完成事件
     */
    public static TransferCompletionEvent failure(Object source,
                                                  String requestId,
                                                  String transactionHash,
                                                  String chainName,
                                                  String transferType,
                                                  Long transferRecordId,
                                                  String errorMessage,
                                                  String coinType,
                                                  String amount,
                                                  String tenantId,
                                                  String tokenValue,
                                                  String fromAddress,
                                                  String toAddress
    ) {
        return new TransferCompletionEvent(source, requestId, transactionHash, chainName,
            transferType, false, transferRecordId, errorMessage, 0, coinType, amount, tenantId, tokenValue, fromAddress, toAddress);
    }

    /**
     * 获取格式化的事件信息
     * 用于日志记录和调试
     *
     * @return 格式化的事件信息
     */
    public String getFormattedInfo() {
        return String.format("TransferCompletion[requestId=%s, txHash=%s, chain=%s, type=%s, success=%s, recordId=%s]",
            requestId, transactionHash, chainName, transferType, success, transferRecordId);
    }

    /**
     * 获取事件描述
     * 用于监控和告警
     *
     * @return 事件描述
     */
    public String getDescription() {
        String status = success ? "成功" : "失败";
        return String.format("%s链%s转账%s", chainName, transferType, status);
    }

    /**
     * 检查事件是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return requestId != null && !requestId.trim().isEmpty()
            && transactionHash != null && !transactionHash.trim().isEmpty()
            && chainName != null && !chainName.trim().isEmpty()
            && transferType != null && !transferType.trim().isEmpty()
            && transferRecordId != null && transferRecordId > 0;
    }

    /**
     * 是否为提现完成事件
     *
     * @return 是否为提现
     */
    public boolean isWithdrawal() {
        return "withdraw".equalsIgnoreCase(transferType);
    }

    /**
     * 是否为充值完成事件
     *
     * @return 是否为充值
     */
    public boolean isDeposit() {
        return "deposit".equalsIgnoreCase(transferType);
    }

    /**
     * 是否为归集完成事件
     *
     * @return 是否为归集
     */
    public boolean isCollection() {
        return "collect".equalsIgnoreCase(transferType);
    }

}
